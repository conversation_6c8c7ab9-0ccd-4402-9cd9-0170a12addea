-- 开发环境测试数据

-- 插入测试说明书数据
INSERT INTO documentation (
    entry_point_id, entry_point_name, title, content, level, status, 
    version, is_final_version, project_id, branch_name, created_at, updated_at
) VALUES 
(
    'com.webank.maling.service.UserService.createUser',
    'UserService.createUser',
    '用户创建服务说明书',
    '# 用户创建服务\n\n这是一个用于创建新用户的服务方法。\n\n## 功能描述\n- 验证用户输入\n- 创建用户记录\n- 发送欢迎邮件',
    1,
    'COMPLETED',
    1,
    0,
    'maling-graph',
    'main',
    NOW(),
    NOW()
),
(
    'com.webank.maling.service.OrderService.processOrder',
    'OrderService.processOrder',
    '订单处理服务说明书',
    '# 订单处理服务\n\n处理用户订单的核心服务方法。\n\n## 处理流程\n1. 验证订单信息\n2. 计算订单金额\n3. 扣减库存\n4. 生成订单记录',
    2,
    'COMPLETED',
    1,
    1,
    'maling-graph',
    'main',
    NOW(),
    NOW()
);

-- 插入测试任务数据
INSERT INTO documentation_task (
    entry_point_id, entry_point_name, target_level, current_level, 
    status, progress, project_id, branch_name, created_at, updated_at
) VALUES 
(
    'com.webank.maling.service.UserService.createUser',
    'UserService.createUser',
    3,
    3,
    'COMPLETED',
    100,
    'maling-graph',
    'main',
    NOW(),
    NOW()
),
(
    'com.webank.maling.service.OrderService.processOrder',
    'OrderService.processOrder',
    2,
    2,
    'COMPLETED',
    100,
    'maling-graph',
    'main',
    NOW(),
    NOW()
),
(
    'com.webank.maling.service.PaymentService.processPayment',
    'PaymentService.processPayment',
    3,
    1,
    'RUNNING',
    33,
    'maling-graph',
    'main',
    NOW(),
    NOW()
);

-- 插入测试方法信息数据
INSERT INTO documentation_method (
    documentation_id, method_id, method_name, method_type, call_level,
    description, signature, class_name, created_at
) VALUES 
(
    1,
    'com.webank.maling.service.UserService.createUser',
    'com.webank.maling.service.UserService.createUser',
    'ENTRY_POINT',
    0,
    '创建新用户的主要方法',
    'public User createUser(UserCreateRequest request)',
    'com.webank.maling.service.UserService',
    NOW()
),
(
    1,
    'com.webank.maling.service.UserService.validateUser',
    'com.webank.maling.service.UserService.validateUser',
    'INTERNAL',
    1,
    '验证用户输入信息',
    'private boolean validateUser(UserCreateRequest request)',
    'com.webank.maling.service.UserService',
    NOW()
),
(
    2,
    'com.webank.maling.service.OrderService.processOrder',
    'com.webank.maling.service.OrderService.processOrder',
    'ENTRY_POINT',
    0,
    '处理订单的主要方法',
    'public OrderResult processOrder(OrderRequest request)',
    'com.webank.maling.service.OrderService',
    NOW()
),
(
    2,
    'com.webank.maling.service.OrderService.calculateAmount',
    'com.webank.maling.service.OrderService.calculateAmount',
    'INTERNAL',
    1,
    '计算订单总金额',
    'private BigDecimal calculateAmount(OrderRequest request)',
    'com.webank.maling.service.OrderService',
    NOW()
);
