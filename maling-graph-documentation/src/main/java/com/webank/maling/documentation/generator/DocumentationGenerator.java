package com.webank.maling.documentation.generator;

import com.webank.maling.ai.documentation.AIDocumentationService;
import com.webank.maling.documentation.config.DocumentationConfig;
import com.webank.maling.base.model.MethodInfo;
import com.webank.maling.documentation.entity.Documentation;
import com.webank.maling.documentation.entity.DocumentationTask;
import com.webank.maling.documentation.service.*;
import com.webank.maling.repository.nebula.NebulaGraphClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 文档生成器主类
 * 整个说明书生成系统的入口
 *
 * <AUTHOR> Graph Team
 */
@Slf4j
@Component
public class DocumentationGenerator {

    private final DocumentationConfig config;
    private final NebulaGraphClient nebulaGraphClient;

    // 服务组件
    private final EntryPointService entryPointService;
    private final SubgraphService subgraphService;
    private final AIDocumentationService aiDocumentationService;
    private final DocumentationArchiveService archiveService;
    private final DocumentationTaskService taskService;
    private final ProgressiveDocumentationService progressiveService;

    // Spring管理的线程池
    private final Executor taskExecutor;

    @Autowired
    public DocumentationGenerator(
            DocumentationConfig config,
            NebulaGraphClient nebulaGraphClient,
            EntryPointService entryPointService,
            SubgraphService subgraphService,
            AIDocumentationService aiDocumentationService,
            DocumentationArchiveService archiveService,
            DocumentationTaskService taskService,
            ProgressiveDocumentationService progressiveService,
            Executor taskExecutor) {

        this.config = config;
        this.nebulaGraphClient = nebulaGraphClient;
        this.entryPointService = entryPointService;
        this.subgraphService = subgraphService;
        this.aiDocumentationService = aiDocumentationService;
        this.archiveService = archiveService;
        this.taskService = taskService;
        this.progressiveService = progressiveService;
        this.taskExecutor = taskExecutor;

        log.info("文档生成器初始化完成");
    }
    
    /**
     * 为所有入口点生成说明书
     *
     * @param targetLevel 目标层级（1-3）
     * @return 生成任务的Future列表
     */
    @Async("taskExecutor")
    public CompletableFuture<List<Long>> generateDocumentationForAllEntryPoints(int targetLevel) {
        try {
            log.info("开始为所有入口点生成 {} 层级的说明书", targetLevel);

            // 1. 获取所有入口点
            List<MethodInfo> entryPoints = entryPointService.getAllEntryPoints();
            log.info("找到 {} 个入口点", entryPoints.size());

            if (entryPoints.isEmpty()) {
                log.warn("没有找到任何入口点");
                return CompletableFuture.completedFuture(List.of());
            }

            // 2. 为每个入口点启动生成任务
            List<CompletableFuture<Long>> futures = entryPoints.stream()
                    .map(entryPoint -> generateDocumentationForEntryPoint(entryPoint.getMethodId(), targetLevel))
                    .toList();

            // 3. 等待所有任务完成
            CompletableFuture<Void> allTasks = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0]));

            return allTasks.thenApply(v ->
                    futures.stream()
                            .map(CompletableFuture::join)
                            .toList()
            );

        } catch (Exception e) {
            log.error("为所有入口点生成说明书时发生错误", e);
            return CompletableFuture.failedFuture(new RuntimeException("批量生成失败", e));
        }
    }
    
    /**
     * 为指定入口点生成说明书
     *
     * @param entryPointId 入口点ID
     * @param targetLevel 目标层级（1-3）
     * @return 任务ID
     */
    @Async("taskExecutor")
    public CompletableFuture<Long> generateDocumentationForEntryPoint(String entryPointId, int targetLevel) {
        try {
            log.info("开始为入口点 {} 生成 {} 层级的说明书", entryPointId, targetLevel);

            // 验证参数
            if (targetLevel < 1 || targetLevel > 3) {
                throw new IllegalArgumentException("目标层级必须在1-3之间");
            }

            // 检查入口点是否存在
            MethodInfo entryPoint = entryPointService.getEntryPointById(entryPointId);
            if (entryPoint == null) {
                throw new IllegalArgumentException("入口点不存在: " + entryPointId);
            }

            // 检查是否已有正在进行的任务
            DocumentationTask existingTask = taskService.getRunningTaskForEntryPoint(entryPointId);
            if (existingTask != null) {
                log.warn("入口点 {} 已有正在进行的生成任务: {}", entryPointId, existingTask.getId());
                return CompletableFuture.completedFuture(existingTask.getId());
            }

            // 启动渐进式生成
            return progressiveService.startProgressiveGeneration(entryPointId, targetLevel);

        } catch (Exception e) {
            log.error("为入口点 {} 生成说明书时发生错误", entryPointId, e);
            return CompletableFuture.failedFuture(new RuntimeException("生成失败", e));
        }
    }
    
    /**
     * 获取生成任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    public DocumentationTask getTaskStatus(Long taskId) {
        return taskService.getTaskById(taskId);
    }
    
    /**
     * 获取入口点的说明书
     * 
     * @param entryPointId 入口点ID
     * @param level 层级（可选，不指定则返回最高层级）
     * @return 说明书
     */
    public Documentation getDocumentation(String entryPointId, Integer level) {
        // TODO: 实现获取说明书的逻辑
        throw new UnsupportedOperationException("需要实现数据访问层");
    }
    
    /**
     * 取消生成任务
     * 
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    public boolean cancelTask(Long taskId) {
        try {
            return taskService.cancelTask(taskId);
        } catch (Exception e) {
            log.error("取消任务 {} 时发生错误", taskId, e);
            return false;
        }
    }
    
    /**
     * 清理过期数据
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> cleanupExpiredData() {
        try {
            log.info("开始清理过期数据");

            // 清理过期的归档数据
            archiveService.cleanupExpiredArchives().join();

            // 清理过期的任务
            taskService.cleanupExpiredTasks().join();

            log.info("过期数据清理完成");
            return CompletableFuture.completedFuture(null);

        } catch (Exception e) {
            log.error("清理过期数据时发生错误", e);
            return CompletableFuture.failedFuture(e);
        }
    }
    
    /**
     * 获取系统统计信息
     */
    public DocumentationStatistics getStatistics() {
        try {
            return DocumentationStatistics.builder()
                    .totalEntryPoints(entryPointService.getTotalEntryPointsCount())
                    .totalDocumentations(getDocumentationCount())
                    .runningTasks(taskService.getRunningTasksCount())
                    .completedTasks(taskService.getCompletedTasksCount())
                    .failedTasks(taskService.getFailedTasksCount())
                    .build();
        } catch (Exception e) {
            log.error("获取统计信息时发生错误", e);
            return DocumentationStatistics.builder().build();
        }
    }
    
    private int getDocumentationCount() {
        // TODO: 实现获取文档总数的逻辑
        return 0;
    }
    
    @PreDestroy
    public void destroy() {
        try {
            log.info("正在关闭文档生成器...");

            // Spring会自动管理线程池和其他Bean的生命周期
            // 这里只需要做一些清理工作

            log.info("文档生成器已关闭");

        } catch (Exception e) {
            log.error("关闭文档生成器时发生错误", e);
        }
    }
    
    /**
     * 统计信息DTO
     */
    public static class DocumentationStatistics {
        private int totalEntryPoints;
        private int totalDocumentations;
        private int runningTasks;
        private int completedTasks;
        private int failedTasks;
        
        public static DocumentationStatisticsBuilder builder() {
            return new DocumentationStatisticsBuilder();
        }
        
        // Builder and getters implementation
        public static class DocumentationStatisticsBuilder {
            private int totalEntryPoints;
            private int totalDocumentations;
            private int runningTasks;
            private int completedTasks;
            private int failedTasks;
            
            public DocumentationStatisticsBuilder totalEntryPoints(int totalEntryPoints) {
                this.totalEntryPoints = totalEntryPoints;
                return this;
            }
            
            public DocumentationStatisticsBuilder totalDocumentations(int totalDocumentations) {
                this.totalDocumentations = totalDocumentations;
                return this;
            }
            
            public DocumentationStatisticsBuilder runningTasks(int runningTasks) {
                this.runningTasks = runningTasks;
                return this;
            }
            
            public DocumentationStatisticsBuilder completedTasks(int completedTasks) {
                this.completedTasks = completedTasks;
                return this;
            }
            
            public DocumentationStatisticsBuilder failedTasks(int failedTasks) {
                this.failedTasks = failedTasks;
                return this;
            }
            
            public DocumentationStatistics build() {
                DocumentationStatistics stats = new DocumentationStatistics();
                stats.totalEntryPoints = this.totalEntryPoints;
                stats.totalDocumentations = this.totalDocumentations;
                stats.runningTasks = this.runningTasks;
                stats.completedTasks = this.completedTasks;
                stats.failedTasks = this.failedTasks;
                return stats;
            }
        }
        
        // Getters
        public int getTotalEntryPoints() { return totalEntryPoints; }
        public int getTotalDocumentations() { return totalDocumentations; }
        public int getRunningTasks() { return runningTasks; }
        public int getCompletedTasks() { return completedTasks; }
        public int getFailedTasks() { return failedTasks; }
    }
}
