# Spring Boot Application Configuration
spring.application.name=maling-graph-documentation

# Database Configuration - MySQL
spring.datasource.url=${DB_URL:**************************************************************************************************************************************************************}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:password}

# HikariCP Connection Pool Configuration
spring.datasource.hikari.pool-name=DocumentationHikariCP
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-test-query=SELECT 1

# MyBatis Configuration
mybatis.type-aliases-package=com.webank.maling.documentation.entity
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.lazy-loading-enabled=true
mybatis.configuration.cache-enabled=true
mybatis.configuration.log-impl=org.apache.ibatis.logging.slf4j.Slf4jImpl

# Server Configuration
server.port=8080
server.servlet.context-path=/documentation

# Logging Configuration
logging.level.com.webank.maling.documentation=DEBUG
logging.level.com.webank.maling.documentation.mapper=DEBUG
logging.level.org.springframework.jdbc=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Documentation Generation Configuration
# Level 1 Configuration
documentation.level1.max_steps=2
documentation.level1.max_length=1000

# Level 2 Configuration
documentation.level2.max_steps=5
documentation.level2.max_length=3000

# Level 3 Configuration
documentation.level3.max_steps=10
documentation.level3.max_length=10000

# AI Configuration
documentation.ai.max_tokens=4000
documentation.ai.max_retry=3
documentation.ai.retry_delay_ms=1000

# Archive Configuration
documentation.archive.auto_enabled=true
documentation.archive.delay_days=7
documentation.archive.max_intermediate_versions=5

# Concurrent Configuration
documentation.concurrent.max_tasks=3
documentation.concurrent.timeout_minutes=30

# Storage Configuration
documentation.storage.path=documentation
documentation.storage.use_database=true
documentation.storage.use_filesystem=false
