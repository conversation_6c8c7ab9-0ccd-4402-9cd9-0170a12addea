package com.webank.maling.repository.documentation.impl;

import com.webank.maling.base.util.Json;
import com.webank.maling.documentation.entity.Documentation;
import com.webank.maling.repository.documentation.DocumentationRepository;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;

/**
 * 基于文件系统的说明书数据访问实现
 * 
 * <AUTHOR> Graph Team
 */
@Slf4j
public class FileSystemDocumentationRepository implements DocumentationRepository {
    
    private final Path storageRoot;
    private final AtomicLong idGenerator;
    
    public FileSystemDocumentationRepository(String storagePath) {
        this.storageRoot = Paths.get(storagePath, "documentation");
        this.idGenerator = new AtomicLong(1);
        initializeStorage();
    }
    
    private void initializeStorage() {
        try {
            Files.createDirectories(storageRoot);
            log.info("初始化文档存储目录: {}", storageRoot.toAbsolutePath());
        } catch (IOException e) {
            log.error("创建存储目录失败", e);
            throw new RuntimeException("初始化存储失败", e);
        }
    }
    
    @Override
    public Documentation save(Documentation documentation) {
        try {
            if (documentation.getId() == null) {
                documentation.setId(idGenerator.getAndIncrement());
                documentation.setCreatedAt(LocalDateTime.now());
            }
            documentation.setUpdatedAt(LocalDateTime.now());
            
            Path filePath = getDocumentationPath(documentation.getId());
            String json = Json.toJson(documentation);
            Files.writeString(filePath, json);
            
            log.debug("保存说明书: {}", documentation.getId());
            return documentation;
            
        } catch (Exception e) {
            log.error("保存说明书失败", e);
            throw new RuntimeException("保存失败", e);
        }
    }
    
    @Override
    public Optional<Documentation> findById(Long id) {
        try {
            Path filePath = getDocumentationPath(id);
            if (!Files.exists(filePath)) {
                return Optional.empty();
            }
            
            String json = Files.readString(filePath);
            Documentation documentation = Json.fromJson(json, Documentation.class);
            return Optional.ofNullable(documentation);
            
        } catch (Exception e) {
            log.error("查找说明书失败: {}", id, e);
            return Optional.empty();
        }
    }
    
    @Override
    public List<Documentation> findByEntryPointId(String entryPointId) {
        return findAllDocumentations().stream()
                .filter(doc -> entryPointId.equals(doc.getEntryPointId()))
                .toList();
    }
    
    @Override
    public Optional<Documentation> findByEntryPointIdAndLevel(String entryPointId, Integer level) {
        return findAllDocumentations().stream()
                .filter(doc -> entryPointId.equals(doc.getEntryPointId()) && 
                              level.equals(doc.getLevel()))
                .findFirst();
    }
    
    @Override
    public Optional<Documentation> findFinalVersionByEntryPointId(String entryPointId) {
        return findAllDocumentations().stream()
                .filter(doc -> entryPointId.equals(doc.getEntryPointId()) && 
                              Boolean.TRUE.equals(doc.getIsFinalVersion()))
                .findFirst();
    }
    
    @Override
    public List<Documentation> findIntermediateVersionsByEntryPointId(String entryPointId) {
        return findAllDocumentations().stream()
                .filter(doc -> entryPointId.equals(doc.getEntryPointId()) && 
                              Boolean.FALSE.equals(doc.getIsFinalVersion()))
                .toList();
    }
    
    @Override
    public List<Documentation> findByProjectId(String projectId) {
        return findAllDocumentations().stream()
                .filter(doc -> projectId.equals(doc.getProjectId()))
                .toList();
    }
    
    @Override
    public List<Documentation> findByStatus(Documentation.DocumentationStatus status) {
        return findAllDocumentations().stream()
                .filter(doc -> status.equals(doc.getStatus()))
                .toList();
    }
    
    @Override
    public List<Documentation> findByCreatedAtBefore(LocalDateTime createdBefore) {
        return findAllDocumentations().stream()
                .filter(doc -> doc.getCreatedAt() != null && 
                              doc.getCreatedAt().isBefore(createdBefore))
                .toList();
    }
    
    @Override
    public Documentation update(Documentation documentation) {
        if (documentation.getId() == null) {
            throw new IllegalArgumentException("更新的实体必须有ID");
        }
        return save(documentation);
    }
    
    @Override
    public boolean deleteById(Long id) {
        try {
            Path filePath = getDocumentationPath(id);
            boolean deleted = Files.deleteIfExists(filePath);
            if (deleted) {
                log.debug("删除说明书: {}", id);
            }
            return deleted;
        } catch (Exception e) {
            log.error("删除说明书失败: {}", id, e);
            return false;
        }
    }
    
    @Override
    public int deleteByIds(List<Long> ids) {
        int deletedCount = 0;
        for (Long id : ids) {
            if (deleteById(id)) {
                deletedCount++;
            }
        }
        return deletedCount;
    }
    
    @Override
    public long count() {
        return findAllDocumentations().size();
    }
    
    @Override
    public long countByProjectId(String projectId) {
        return findByProjectId(projectId).size();
    }
    
    @Override
    public long countByStatus(Documentation.DocumentationStatus status) {
        return findByStatus(status).size();
    }
    
    @Override
    public boolean existsByEntryPointId(String entryPointId) {
        return findByEntryPointId(entryPointId).size() > 0;
    }
    
    @Override
    public boolean existsByEntryPointIdAndLevel(String entryPointId, Integer level) {
        return findByEntryPointIdAndLevel(entryPointId, level).isPresent();
    }
    
    /**
     * 查找所有说明书
     */
    private List<Documentation> findAllDocumentations() {
        try {
            if (!Files.exists(storageRoot)) {
                return List.of();
            }
            
            try (Stream<Path> paths = Files.list(storageRoot)) {
                return paths
                        .filter(path -> path.toString().endsWith(".json"))
                        .map(this::loadDocumentationFromPath)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .toList();
            }
        } catch (Exception e) {
            log.error("查找所有说明书失败", e);
            return List.of();
        }
    }
    
    /**
     * 从路径加载说明书
     */
    private Optional<Documentation> loadDocumentationFromPath(Path path) {
        try {
            String json = Files.readString(path);
            Documentation documentation = Json.fromJson(json, Documentation.class);
            return Optional.ofNullable(documentation);
        } catch (Exception e) {
            log.warn("加载说明书文件失败: {}", path, e);
            return Optional.empty();
        }
    }
    
    /**
     * 获取说明书文件路径
     */
    private Path getDocumentationPath(Long id) {
        return storageRoot.resolve("doc_" + id + ".json");
    }
}
