spring:
  application:
    name: maling-graph-documentation
  
  # 数据源配置
  datasource:
    # H2数据库配置（开发环境）
    url: jdbc:h2:mem:documentation;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
    # MySQL数据库配置（生产环境）
    # url: *********************************************************************************************************************************
    # driver-class-name: com.mysql.cj.jdbc.Driver
    # username: root
    # password: password
    
  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true

# MyBatis配置
mybatis:
  # 实体类别名包路径
  type-aliases-package: com.webank.maling.documentation.entity
  # 映射文件路径
  mapper-locations: classpath:mapper/*.xml
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启延迟加载
    lazy-loading-enabled: true
    # 开启二级缓存
    cache-enabled: true
    # 日志实现
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /documentation

# 日志配置
logging:
  level:
    com.webank.maling.documentation: DEBUG
    com.webank.maling.documentation.mapper: DEBUG
    org.springframework.jdbc: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 文档生成配置
documentation:
  # 渐进式生成配置
  level1:
    max_steps: 2
    max_length: 1000
  level2:
    max_steps: 5
    max_length: 3000
  level3:
    max_steps: 10
    max_length: 10000
  
  # AI生成配置
  ai:
    max_tokens: 4000
    max_retry: 3
    retry_delay_ms: 1000
  
  # 归档策略配置
  archive:
    auto_enabled: true
    delay_days: 7
    max_intermediate_versions: 5
  
  # 并发控制配置
  concurrent:
    max_tasks: 3
    timeout_minutes: 30
  
  # 存储配置
  storage:
    path: documentation
    use_database: true
    use_filesystem: false
