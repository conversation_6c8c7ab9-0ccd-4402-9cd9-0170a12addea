# 开发环境配置
spring:
  profiles:
    active: dev
  
  # 数据源配置 - 开发环境MySQL
  datasource:
    url: ${DB_URL:******************************************************************************************************************************************************************}
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    
    # 连接池配置
    hikari:
      pool-name: DocumentationDevHikariCP
      minimum-idle: 2
      maximum-pool-size: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
  
  # 数据库初始化配置
  sql:
    init:
      # 启动时执行schema.sql
      mode: always
      schema-locations: classpath:schema.sql
      # 编码设置
      encoding: utf-8
      # 分隔符
      separator: ;
      # 继续执行即使出错
      continue-on-error: false

# MyBatis配置
mybatis:
  configuration:
    # 开启SQL日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志配置
logging:
  level:
    com.webank.maling.documentation: DEBUG
    com.webank.maling.documentation.mapper: DEBUG
    org.springframework.jdbc: DEBUG
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# 文档生成配置
documentation:
  level1:
    max_steps: 2
    max_length: 1000
  level2:
    max_steps: 5
    max_length: 3000
  level3:
    max_steps: 10
    max_length: 10000
  ai:
    max_tokens: 4000
    max_retry: 3
    retry_delay_ms: 1000
  archive:
    auto_enabled: true
    delay_days: 7
    max_intermediate_versions: 5
  concurrent:
    max_tasks: 3
    timeout_minutes: 30
  storage:
    path: documentation
    use_database: true
    use_filesystem: false
