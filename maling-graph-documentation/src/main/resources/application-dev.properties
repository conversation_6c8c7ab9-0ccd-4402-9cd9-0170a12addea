# Development Environment Configuration

# Database Configuration - Development MySQL
spring.datasource.url=${DB_URL:******************************************************************************************************************************************************************}
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:password}

# HikariCP Connection Pool Configuration - Development (Smaller Pool)
spring.datasource.hikari.pool-name=DocumentationDevHikariCP
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.maximum-pool-size=10

# MyBatis Configuration - Development (Show SQL)
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# Logging Configuration - Development (Verbose Logging)
logging.level.com.webank.maling.documentation=DEBUG
logging.level.com.webank.maling.documentation.mapper=DEBUG
logging.level.org.springframework.jdbc=DEBUG
logging.level.org.mybatis=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n

# Documentation Configuration - Development (Conservative Settings)
documentation.ai.max_retry=3
documentation.ai.retry_delay_ms=1000
documentation.archive.delay_days=7
documentation.archive.max_intermediate_versions=5
documentation.concurrent.max_tasks=3
documentation.concurrent.timeout_minutes=30
documentation.storage.path=documentation
