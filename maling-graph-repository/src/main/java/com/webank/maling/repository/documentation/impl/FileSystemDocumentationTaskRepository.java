package com.webank.maling.repository.documentation.impl;

import com.webank.maling.base.util.Json;
import com.webank.maling.documentation.entity.DocumentationTask;
import com.webank.maling.repository.documentation.DocumentationTaskRepository;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;

/**
 * 基于文件系统的任务数据访问实现
 * 
 * <AUTHOR> Graph Team
 */
@Slf4j
public class FileSystemDocumentationTaskRepository implements DocumentationTaskRepository {
    
    private final Path storageRoot;
    private final AtomicLong idGenerator;
    
    public FileSystemDocumentationTaskRepository(String storagePath) {
        this.storageRoot = Paths.get(storagePath, "tasks");
        this.idGenerator = new AtomicLong(1);
        initializeStorage();
    }
    
    private void initializeStorage() {
        try {
            Files.createDirectories(storageRoot);
            log.info("初始化任务存储目录: {}", storageRoot.toAbsolutePath());
        } catch (IOException e) {
            log.error("创建存储目录失败", e);
            throw new RuntimeException("初始化存储失败", e);
        }
    }
    
    @Override
    public DocumentationTask save(DocumentationTask task) {
        try {
            if (task.getId() == null) {
                task.setId(idGenerator.getAndIncrement());
                task.setCreatedAt(LocalDateTime.now());
            }
            task.setUpdatedAt(LocalDateTime.now());
            
            Path filePath = getTaskPath(task.getId());
            String json = Json.toJson(task);
            Files.writeString(filePath, json);
            
            log.debug("保存任务: {}", task.getId());
            return task;
            
        } catch (Exception e) {
            log.error("保存任务失败", e);
            throw new RuntimeException("保存失败", e);
        }
    }
    
    @Override
    public Optional<DocumentationTask> findById(Long id) {
        try {
            Path filePath = getTaskPath(id);
            if (!Files.exists(filePath)) {
                return Optional.empty();
            }
            
            String json = Files.readString(filePath);
            DocumentationTask task = Json.fromJson(json, DocumentationTask.class);
            return Optional.ofNullable(task);
            
        } catch (Exception e) {
            log.error("查找任务失败: {}", id, e);
            return Optional.empty();
        }
    }
    
    @Override
    public List<DocumentationTask> findByEntryPointId(String entryPointId) {
        return findAllTasks().stream()
                .filter(task -> entryPointId.equals(task.getEntryPointId()))
                .toList();
    }
    
    @Override
    public Optional<DocumentationTask> findRunningTaskByEntryPointId(String entryPointId) {
        return findAllTasks().stream()
                .filter(task -> entryPointId.equals(task.getEntryPointId()))
                .filter(this::isRunningTask)
                .findFirst();
    }
    
    @Override
    public List<DocumentationTask> findByStatus(DocumentationTask.TaskStatus status) {
        return findAllTasks().stream()
                .filter(task -> status.equals(task.getStatus()))
                .toList();
    }
    
    @Override
    public List<DocumentationTask> findRunningTasks() {
        return findAllTasks().stream()
                .filter(this::isRunningTask)
                .toList();
    }
    
    @Override
    public List<DocumentationTask> findByProjectId(String projectId) {
        return findAllTasks().stream()
                .filter(task -> projectId.equals(task.getProjectId()))
                .toList();
    }
    
    @Override
    public List<DocumentationTask> findByCreatedAtBefore(LocalDateTime createdBefore) {
        return findAllTasks().stream()
                .filter(task -> task.getCreatedAt() != null && 
                               task.getCreatedAt().isBefore(createdBefore))
                .toList();
    }
    
    @Override
    public List<DocumentationTask> findByCompletedAtBefore(LocalDateTime completedBefore) {
        return findAllTasks().stream()
                .filter(task -> task.getCompletedAt() != null && 
                               task.getCompletedAt().isBefore(completedBefore))
                .toList();
    }
    
    @Override
    public DocumentationTask update(DocumentationTask task) {
        if (task.getId() == null) {
            throw new IllegalArgumentException("更新的实体必须有ID");
        }
        return save(task);
    }
    
    @Override
    public boolean deleteById(Long id) {
        try {
            Path filePath = getTaskPath(id);
            boolean deleted = Files.deleteIfExists(filePath);
            if (deleted) {
                log.debug("删除任务: {}", id);
            }
            return deleted;
        } catch (Exception e) {
            log.error("删除任务失败: {}", id, e);
            return false;
        }
    }
    
    @Override
    public int deleteByIds(List<Long> ids) {
        int deletedCount = 0;
        for (Long id : ids) {
            if (deleteById(id)) {
                deletedCount++;
            }
        }
        return deletedCount;
    }
    
    @Override
    public int deleteCompletedTasksBefore(LocalDateTime completedBefore) {
        List<DocumentationTask> tasksToDelete = findByCompletedAtBefore(completedBefore);
        List<Long> idsToDelete = tasksToDelete.stream()
                .map(DocumentationTask::getId)
                .toList();
        return deleteByIds(idsToDelete);
    }
    
    @Override
    public long count() {
        return findAllTasks().size();
    }
    
    @Override
    public long countByStatus(DocumentationTask.TaskStatus status) {
        return findByStatus(status).size();
    }
    
    @Override
    public long countRunningTasks() {
        return findRunningTasks().size();
    }
    
    @Override
    public long countByProjectId(String projectId) {
        return findByProjectId(projectId).size();
    }
    
    @Override
    public boolean hasRunningTaskForEntryPoint(String entryPointId) {
        return findRunningTaskByEntryPointId(entryPointId).isPresent();
    }
    
    /**
     * 判断任务是否为运行中状态
     */
    private boolean isRunningTask(DocumentationTask task) {
        return task.getStatus() == DocumentationTask.TaskStatus.PENDING ||
               task.getStatus() == DocumentationTask.TaskStatus.RUNNING;
    }
    
    /**
     * 查找所有任务
     */
    private List<DocumentationTask> findAllTasks() {
        try {
            if (!Files.exists(storageRoot)) {
                return List.of();
            }
            
            try (Stream<Path> paths = Files.list(storageRoot)) {
                return paths
                        .filter(path -> path.toString().endsWith(".json"))
                        .map(this::loadTaskFromPath)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .toList();
            }
        } catch (Exception e) {
            log.error("查找所有任务失败", e);
            return List.of();
        }
    }
    
    /**
     * 从路径加载任务
     */
    private Optional<DocumentationTask> loadTaskFromPath(Path path) {
        try {
            String json = Files.readString(path);
            DocumentationTask task = Json.fromJson(json, DocumentationTask.class);
            return Optional.ofNullable(task);
        } catch (Exception e) {
            log.warn("加载任务文件失败: {}", path, e);
            return Optional.empty();
        }
    }
    
    /**
     * 获取任务文件路径
     */
    private Path getTaskPath(Long id) {
        return storageRoot.resolve("task_" + id + ".json");
    }
}
