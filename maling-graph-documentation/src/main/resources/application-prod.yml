# 生产环境配置
spring:
  profiles:
    active: prod
  
  # 数据源配置 - 生产环境MySQL
  datasource:
    url: ${DB_URL:****************************************************************************************************************************************************************}
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    
    # 连接池配置 - 生产环境优化
    hikari:
      pool-name: DocumentationProdHikariCP
      minimum-idle: 10
      maximum-pool-size: 50
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
      # 连接泄漏检测
      leak-detection-threshold: 60000
  
  # 数据库初始化配置 - 生产环境不自动执行
  sql:
    init:
      mode: never

# MyBatis配置
mybatis:
  configuration:
    # 生产环境关闭SQL日志
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 日志配置
logging:
  level:
    com.webank.maling.documentation: INFO
    com.webank.maling.documentation.mapper: WARN
    org.springframework.jdbc: WARN
    org.mybatis: WARN
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/documentation.log
    max-size: 100MB
    max-history: 30

# 服务器配置
server:
  # 生产环境端口
  port: ${SERVER_PORT:8080}
  # 优雅关闭
  shutdown: graceful
  # 连接超时
  connection-timeout: 20000
  # Tomcat配置
  tomcat:
    # 最大连接数
    max-connections: 8192
    # 最大线程数
    threads:
      max: 200
      min-spare: 10

# 文档生成配置 - 生产环境优化
documentation:
  level1:
    max_steps: 2
    max_length: 1000
  level2:
    max_steps: 5
    max_length: 3000
  level3:
    max_steps: 10
    max_length: 10000
  ai:
    max_tokens: 4000
    max_retry: 5
    retry_delay_ms: 2000
  archive:
    auto_enabled: true
    delay_days: 3
    max_intermediate_versions: 3
  concurrent:
    max_tasks: 10
    timeout_minutes: 60
  storage:
    path: /data/documentation
    use_database: true
    use_filesystem: false

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
