# Spring Bean 迁移说明

## 迁移概述

将原有的手动创建对象的方式改为Spring Bean管理，利用Spring的依赖注入、生命周期管理等特性。

## 迁移的类

### 1. Generator层

#### DocumentationGenerator
- **改动**: 添加`@Component`注解，使用`@Autowired`构造函数注入
- **异步方法**: 使用`@Async("taskExecutor")`替代手动线程池
- **生命周期**: 使用`@PreDestroy`替代`AutoCloseable`

```java
@Component
public class DocumentationGenerator {
    @Autowired
    public DocumentationGenerator(
        DocumentationConfig config,
        NebulaGraphClient nebulaGraphClient,
        EntryPointService entryPointService,
        // ... 其他依赖
    ) { }
    
    @Async("taskExecutor")
    public CompletableFuture<List<Long>> generateDocumentationForAllEntryPoints(int targetLevel) { }
    
    @PreDestroy
    public void destroy() { }
}
```

### 2. Service层

#### EntryPointService
- **改动**: 添加`@Service`注解，使用`@Autowired`构造函数注入

```java
@Service
public class EntryPointService {
    @Autowired
    public EntryPointService(NebulaGraphClient nebulaGraphClient) { }
}
```

#### SubgraphService
- **改动**: 添加`@Service`注解，使用`@Autowired`构造函数注入

```java
@Service
public class SubgraphService {
    @Autowired
    public SubgraphService(NebulaGraphClient nebulaGraphClient) { }
}
```

#### DocumentationArchiveService
- **改动**: 添加`@Service`注解，使用`@Autowired`构造函数注入
- **异步方法**: 使用`@Async("taskExecutor")`

```java
@Service
public class DocumentationArchiveService {
    @Autowired
    public DocumentationArchiveService(DocumentationConfig config) { }
    
    @Async("taskExecutor")
    public CompletableFuture<Void> cleanupExpiredArchives() { }
}
```

#### ProgressiveDocumentationService
- **改动**: 添加`@Service`注解，使用`@Autowired`构造函数注入
- **异步方法**: 使用`@Async("taskExecutor")`

```java
@Service
public class ProgressiveDocumentationService {
    @Autowired
    public ProgressiveDocumentationService(
        DocumentationConfig config,
        SubgraphService subgraphService,
        AIDocumentationService aiDocumentationService,
        DocumentationArchiveService archiveService,
        DocumentationTaskService taskService
    ) { }
    
    @Async("taskExecutor")
    public CompletableFuture<Long> startProgressiveGeneration(String entryPointId, int targetLevel) { }
}
```

#### DocumentationTaskService
- **改动**: 已经是`@Service`，更新构造函数注入`DocumentationConfig`

```java
@Service
public class DocumentationTaskService {
    @Autowired
    public DocumentationTaskService(DocumentationTaskMapper taskMapper, DocumentationConfig config) { }
}
```

## 配置变更

### DocumentationSpringConfig
- **移除**: 手动创建Bean的`@Bean`方法
- **保留**: 基础设施Bean（NebulaGraphClient、线程池等）

```java
@Configuration
public class DocumentationSpringConfig implements AsyncConfigurer {
    
    @Bean
    public NebulaGraphClient nebulaGraphClient() { }
    
    @Bean(name = "taskExecutor")
    public Executor getAsyncExecutor() { }
    
    // 移除了所有Service和Generator的手动Bean配置
}
```

### DocumentationConfig
- **改动**: 添加`@Component`和`@ConfigurationProperties`注解
- **移除**: 单例模式，改为Spring管理

```java
@Component
@ConfigurationProperties(prefix = "documentation")
public class DocumentationConfig {
    // 移除了getInstance()方法和单例模式
}
```

## 优势

### 1. 依赖注入
- **自动装配**: Spring自动解析和注入依赖
- **循环依赖检测**: Spring会检测并报告循环依赖
- **类型安全**: 编译时检查依赖类型

### 2. 生命周期管理
- **自动初始化**: Spring按正确顺序初始化Bean
- **优雅关闭**: 使用`@PreDestroy`进行清理
- **单例管理**: Spring确保Bean的单例性

### 3. 异步处理
- **统一线程池**: 使用Spring管理的线程池
- **异步注解**: `@Async`简化异步方法调用
- **异常处理**: Spring提供统一的异步异常处理

### 4. 配置管理
- **属性绑定**: `@ConfigurationProperties`自动绑定配置
- **环境感知**: 支持不同环境的配置
- **类型转换**: 自动进行类型转换和验证

## 使用方式

### 启动应用
```bash
# 开发环境
./gradlew bootRun --args='--spring.profiles.active=dev'

# 生产环境
./gradlew bootRun --args='--spring.profiles.active=prod'
```

### API调用
```bash
# 生成说明书
curl -X POST "http://localhost:8080/documentation/api/documentation/generate/all?level=3"

# 查看任务状态
curl -X GET "http://localhost:8080/documentation/api/documentation/task/1"
```

### 依赖注入示例
```java
@RestController
public class MyController {
    
    @Autowired
    private DocumentationGenerator documentationGenerator;
    
    @Autowired
    private DocumentationTaskService taskService;
    
    // Spring会自动注入这些依赖
}
```

## 注意事项

### 1. 循环依赖
- 避免服务间的循环依赖
- 使用`@Lazy`注解延迟初始化（如果必要）

### 2. 异步方法
- 异步方法必须是public的
- 不能在同一个类内部调用异步方法
- 返回类型必须是`CompletableFuture`或`void`

### 3. 配置属性
- 配置属性类必须有setter方法或使用`@ConstructorBinding`
- 嵌套配置需要使用静态内部类

### 4. 测试
- 使用`@SpringBootTest`进行集成测试
- 使用`@MockBean`模拟依赖
- 使用`@TestConfiguration`提供测试配置

## 迁移检查清单

- [x] 所有Service类添加`@Service`注解
- [x] 所有Component类添加`@Component`注解
- [x] 构造函数添加`@Autowired`注解
- [x] 异步方法添加`@Async`注解
- [x] 配置类添加`@ConfigurationProperties`注解
- [x] 移除手动Bean创建代码
- [x] 更新生命周期管理方法
- [x] 测试所有功能正常工作
