package com.webank.maling.documentation.example;

import com.webank.maling.ai.documentation.AIDocumentationService;
import com.webank.maling.documentation.entity.Documentation;
import com.webank.maling.documentation.entity.DocumentationTask;
import com.webank.maling.documentation.generator.DocumentationGenerator;
import com.webank.maling.documentation.service.*;
import com.webank.maling.repository.nebula.NebulaGraphClient;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 文档生成器使用示例
 * 
 * <AUTHOR> Graph Team
 */
@Slf4j
public class DocumentationGeneratorExample {
    
    public static void main(String[] args) {
        // 示例1：为所有入口点生成核心流程说明书
        generateCoreDocumentationForAllEntryPoints();
        
        // 示例2：为特定入口点生成完整说明书
        generateCompleteDocumentationForSpecificEntryPoint();
        
        // 示例3：监控生成进度
        monitorGenerationProgress();
        
        // 示例4：获取生成的说明书
        retrieveGeneratedDocumentation();
    }
    
    /**
     * 示例1：为所有入口点生成核心流程说明书（第1层）
     */
    private static void generateCoreDocumentationForAllEntryPoints() {
        log.info("=== 示例1：为所有入口点生成核心流程说明书 ===");
        
        try (NebulaGraphClient nebulaClient = new NebulaGraphClient();
             DocumentationGenerator generator = new DocumentationGenerator(nebulaClient)) {
            
            // 为所有入口点生成第1层（核心流程）说明书
            CompletableFuture<List<Long>> future = generator.generateDocumentationForAllEntryPoints(1);
            
            // 等待完成
            List<Long> taskIds = future.join();
            log.info("已启动 {} 个文档生成任务", taskIds.size());
            
            // 等待所有任务完成
            waitForTasksCompletion(generator, taskIds);
            
        } catch (Exception e) {
            log.error("生成核心流程说明书时发生错误", e);
        }
    }
    
    /**
     * 示例2：为特定入口点生成完整说明书（第3层）
     */
    private static void generateCompleteDocumentationForSpecificEntryPoint() {
        log.info("=== 示例2：为特定入口点生成完整说明书 ===");
        
        try (NebulaGraphClient nebulaClient = new NebulaGraphClient();
             DocumentationGenerator generator = new DocumentationGenerator(nebulaClient)) {
            
            // 假设这是一个入口点ID
            String entryPointId = "com.example.service.UserService.createUser";
            
            // 生成完整说明书（第3层）
            CompletableFuture<Long> future = generator.generateDocumentationForEntryPoint(entryPointId, 3);
            
            Long taskId = future.join();
            log.info("已启动任务 {} 为入口点 {} 生成完整说明书", taskId, entryPointId);
            
            // 监控任务进度
            monitorSingleTask(generator, taskId);
            
        } catch (Exception e) {
            log.error("生成完整说明书时发生错误", e);
        }
    }
    
    /**
     * 示例3：监控生成进度
     */
    private static void monitorGenerationProgress() {
        log.info("=== 示例3：监控生成进度 ===");
        
        try (NebulaGraphClient nebulaClient = new NebulaGraphClient();
             DocumentationGenerator generator = new DocumentationGenerator(nebulaClient)) {
            
            // 获取系统统计信息
            DocumentationGenerator.DocumentationStatistics stats = generator.getStatistics();
            log.info("系统统计信息：");
            log.info("  总入口点数: {}", stats.getTotalEntryPoints());
            log.info("  总文档数: {}", stats.getTotalDocumentations());
            log.info("  运行中任务: {}", stats.getRunningTasks());
            log.info("  已完成任务: {}", stats.getCompletedTasks());
            log.info("  失败任务: {}", stats.getFailedTasks());
            
        } catch (Exception e) {
            log.error("获取进度信息时发生错误", e);
        }
    }
    
    /**
     * 示例4：获取生成的说明书
     */
    private static void retrieveGeneratedDocumentation() {
        log.info("=== 示例4：获取生成的说明书 ===");
        
        try (NebulaGraphClient nebulaClient = new NebulaGraphClient();
             DocumentationGenerator generator = new DocumentationGenerator(nebulaClient)) {
            
            String entryPointId = "com.example.service.UserService.createUser";
            
            // 获取最高层级的说明书
            Documentation documentation = generator.getDocumentation(entryPointId, null);
            
            if (documentation != null) {
                log.info("获取到说明书：");
                log.info("  标题: {}", documentation.getTitle());
                log.info("  层级: {}", documentation.getLevel());
                log.info("  状态: {}", documentation.getStatus());
                log.info("  是否最终版本: {}", documentation.getIsFinalVersion());
                log.info("  内容长度: {} 字符", documentation.getContent().length());
                log.info("  创建时间: {}", documentation.getCreatedAt());
            } else {
                log.warn("未找到入口点 {} 的说明书", entryPointId);
            }
            
        } catch (Exception e) {
            log.error("获取说明书时发生错误", e);
        }
    }
    
    /**
     * 等待任务完成
     */
    private static void waitForTasksCompletion(DocumentationGenerator generator, List<Long> taskIds) {
        log.info("等待 {} 个任务完成...", taskIds.size());
        
        boolean allCompleted = false;
        int checkCount = 0;
        final int maxChecks = 60; // 最多检查60次（5分钟）
        
        while (!allCompleted && checkCount < maxChecks) {
            try {
                Thread.sleep(5000); // 每5秒检查一次
                checkCount++;
                
                int completed = 0;
                int failed = 0;
                int running = 0;
                
                for (Long taskId : taskIds) {
                    DocumentationTask task = generator.getTaskStatus(taskId);
                    if (task != null) {
                        switch (task.getStatus()) {
                            case COMPLETED -> completed++;
                            case FAILED, CANCELLED -> failed++;
                            case RUNNING -> running++;
                        }
                    }
                }
                
                log.info("任务进度 - 已完成: {}, 失败: {}, 运行中: {}, 总计: {}", 
                        completed, failed, running, taskIds.size());
                
                allCompleted = (completed + failed) == taskIds.size();
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待任务完成时被中断");
                break;
            }
        }
        
        if (allCompleted) {
            log.info("所有任务已完成");
        } else {
            log.warn("等待超时，部分任务可能仍在运行");
        }
    }
    
    /**
     * 监控单个任务
     */
    private static void monitorSingleTask(DocumentationGenerator generator, Long taskId) {
        log.info("监控任务 {} 的进度...", taskId);
        
        boolean completed = false;
        int checkCount = 0;
        final int maxChecks = 60;
        
        while (!completed && checkCount < maxChecks) {
            try {
                Thread.sleep(3000); // 每3秒检查一次
                checkCount++;
                
                DocumentationTask task = generator.getTaskStatus(taskId);
                if (task != null) {
                    log.info("任务 {} - 状态: {}, 当前层级: {}/{}, 进度: {}%", 
                            taskId, task.getStatus(), task.getCurrentLevel(), 
                            task.getTargetLevel(), task.getProgress());
                    
                    completed = task.getStatus() == DocumentationTask.TaskStatus.COMPLETED ||
                               task.getStatus() == DocumentationTask.TaskStatus.FAILED ||
                               task.getStatus() == DocumentationTask.TaskStatus.CANCELLED;
                    
                    if (task.getStatus() == DocumentationTask.TaskStatus.FAILED) {
                        log.error("任务失败，错误信息: {}", task.getErrorMessage());
                    }
                } else {
                    log.warn("无法获取任务 {} 的状态", taskId);
                    break;
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("监控任务时被中断");
                break;
            }
        }
        
        if (completed) {
            log.info("任务 {} 已完成", taskId);
        } else {
            log.warn("监控超时，任务 {} 可能仍在运行", taskId);
        }
    }
}
