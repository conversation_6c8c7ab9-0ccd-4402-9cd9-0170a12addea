package com.webank.maling.repository.documentation;

import com.webank.maling.repository.documentation.impl.FileSystemDocumentationRepository;
import com.webank.maling.repository.documentation.impl.FileSystemDocumentationTaskRepository;
import lombok.extern.slf4j.Slf4j;

/**
 * 文档Repository工厂类
 * 根据配置创建相应的Repository实现
 * 
 * <AUTHOR> Graph Team
 */
@Slf4j
public class DocumentationRepositoryFactory {
    
    private static final String DEFAULT_STORAGE_PATH = "storage";
    
    /**
     * 创建说明书Repository
     * 
     * @param storageType 存储类型（filesystem, database等）
     * @param storagePath 存储路径
     * @return Repository实例
     */
    public static DocumentationRepository createDocumentationRepository(String storageType, String storagePath) {
        try {
            String actualStoragePath = storagePath != null ? storagePath : DEFAULT_STORAGE_PATH;
            
            return switch (storageType.toLowerCase()) {
                case "filesystem", "file" -> {
                    log.info("创建文件系统说明书Repository，存储路径: {}", actualStoragePath);
                    yield new FileSystemDocumentationRepository(actualStoragePath);
                }
                case "database", "db" -> {
                    log.info("创建数据库说明书Repository");
                    // TODO: 实现数据库版本
                    throw new UnsupportedOperationException("数据库存储暂未实现");
                }
                default -> {
                    log.warn("未知的存储类型: {}，使用默认的文件系统存储", storageType);
                    yield new FileSystemDocumentationRepository(actualStoragePath);
                }
            };
        } catch (Exception e) {
            log.error("创建说明书Repository失败", e);
            throw new RuntimeException("Repository创建失败", e);
        }
    }
    
    /**
     * 创建任务Repository
     * 
     * @param storageType 存储类型
     * @param storagePath 存储路径
     * @return Repository实例
     */
    public static DocumentationTaskRepository createTaskRepository(String storageType, String storagePath) {
        try {
            String actualStoragePath = storagePath != null ? storagePath : DEFAULT_STORAGE_PATH;
            
            return switch (storageType.toLowerCase()) {
                case "filesystem", "file" -> {
                    log.info("创建文件系统任务Repository，存储路径: {}", actualStoragePath);
                    yield new FileSystemDocumentationTaskRepository(actualStoragePath);
                }
                case "database", "db" -> {
                    log.info("创建数据库任务Repository");
                    // TODO: 实现数据库版本
                    throw new UnsupportedOperationException("数据库存储暂未实现");
                }
                default -> {
                    log.warn("未知的存储类型: {}，使用默认的文件系统存储", storageType);
                    yield new FileSystemDocumentationTaskRepository(actualStoragePath);
                }
            };
        } catch (Exception e) {
            log.error("创建任务Repository失败", e);
            throw new RuntimeException("Repository创建失败", e);
        }
    }
    
    /**
     * 创建方法信息Repository
     * 
     * @param storageType 存储类型
     * @param storagePath 存储路径
     * @return Repository实例
     */
    public static DocumentationMethodRepository createMethodRepository(String storageType, String storagePath) {
        try {
            String actualStoragePath = storagePath != null ? storagePath : DEFAULT_STORAGE_PATH;
            
            return switch (storageType.toLowerCase()) {
                case "filesystem", "file" -> {
                    log.info("创建文件系统方法Repository，存储路径: {}", actualStoragePath);
                    // TODO: 实现文件系统方法Repository
                    throw new UnsupportedOperationException("文件系统方法Repository暂未实现");
                }
                case "database", "db" -> {
                    log.info("创建数据库方法Repository");
                    // TODO: 实现数据库版本
                    throw new UnsupportedOperationException("数据库存储暂未实现");
                }
                default -> {
                    log.warn("未知的存储类型: {}，方法Repository暂未实现", storageType);
                    throw new UnsupportedOperationException("方法Repository暂未实现");
                }
            };
        } catch (Exception e) {
            log.error("创建方法Repository失败", e);
            throw new RuntimeException("Repository创建失败", e);
        }
    }
    
    /**
     * 使用默认配置创建说明书Repository
     */
    public static DocumentationRepository createDefaultDocumentationRepository() {
        return createDocumentationRepository("filesystem", DEFAULT_STORAGE_PATH);
    }
    
    /**
     * 使用默认配置创建任务Repository
     */
    public static DocumentationTaskRepository createDefaultTaskRepository() {
        return createTaskRepository("filesystem", DEFAULT_STORAGE_PATH);
    }
    
    /**
     * 存储类型枚举
     */
    public enum StorageType {
        FILESYSTEM("filesystem"),
        DATABASE("database");
        
        private final String value;
        
        StorageType(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        public static StorageType fromString(String value) {
            for (StorageType type : values()) {
                if (type.value.equalsIgnoreCase(value)) {
                    return type;
                }
            }
            return FILESYSTEM; // 默认值
        }
    }
}
